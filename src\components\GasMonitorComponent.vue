<template>
  <div class="gas-monitor-container">
    <!-- 头部区域 -->
    <div class="header-section">
      <div class="left-section">
        <div class="realtime-label">实时</div>
        <div class="date-picker-wrapper">
          <el-date-picker v-model="selectedDate" type="date" placeholder="选择日期" format="YYYY.MM.DD"
            value-format="YYYY-MM-DD" @change="handleDateChange" class="custom-date-picker" />
        </div>
      </div>
    </div>

    <!-- 数据展示区 -->
    <div class="data-display-section">
      <!-- CO仪表盘 -->
      <div class="gauge-container">
        <div ref="coGaugeRef" class="gauge-chart"></div>
      </div>

      <!-- CO₂仪表盘 -->
      <div class="gauge-container">
        <div ref="co2GaugeRef" class="gauge-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { ElDatePicker } from 'element-plus'

// Props
const props = defineProps({
  // CO数值
  coValue: {
    type: Number,
    default: 12
  },
  // CO₂数值
  co2Value: {
    type: Number,
    default: 36
  },
  // 初始日期
  initialDate: {
    type: String,
    default: '2025-07-01'
  }
})

// Emits
const emit = defineEmits(['dateChange', 'dataUpdate'])

// 响应式数据
const selectedDate = ref(props.initialDate)
const coGaugeRef = ref(null)
const co2GaugeRef = ref(null)
const coGaugeInstance = ref(null)
const co2GaugeInstance = ref(null)

// 处理日期变化
const handleDateChange = (date) => {
  emit('dateChange', date)
}

// 创建仪表盘配置 - 基于dashboard.html的样式
const createGaugeOption = (value, label, maxValue = 100) => {
  // 计算数值百分比
  const percentage = (value / maxValue) * 100

  // 根据数值确定颜色和状态
  const getStatusInfo = (val) => {
    if (val <= 30) {
      return { color: '#52c41a', status: '良好', statusColor: '#52c41a' }
    } else if (val <= 60) {
      return { color: '#faad14', status: '一般', statusColor: '#faad14' }
    } else {
      return { color: '#f5222d', status: '较差', statusColor: '#f5222d' }
    }
  }

  const statusInfo = getStatusInfo(value)

  return {
    backgroundColor: '#fff',
    series: [
      {
        // 外层仪表盘 - 进度条
        type: 'gauge',
        radius: '70%',
        startAngle: 215,
        endAngle: -35,
        splitNumber: 50,
        detail: {
          offsetCenter: [0, -20],
          formatter: ' '
        },
        pointer: {
          show: false
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: [
              [0, '#7691FA'],
              [percentage / 100, statusInfo.color],
              [1, '#e9e9e9']
            ],
            width: 35
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: true,
          length: 35,
          lineStyle: {
            color: '#fff',
            width: 4
          }
        },
        axisLabel: {
          show: false
        }
      },
      {
        // 内层仪表盘 - 数据显示
        type: 'gauge',
        radius: '58%',
        startAngle: 212,
        endAngle: -32,
        splitNumber: 45,
        pointer: {
          show: false
        },
        detail: {
          offsetCenter: [0, -5],
          formatter: `{a|${label}监测}\n{b|${value}}\n{x|${statusInfo.status}}\n`,
          rich: {
            a: {
              color: '#404346',
              lineHeight: 28,
              fontSize: 18,
              fontWeight: 550
            },
            b: {
              color: statusInfo.statusColor,
              fontSize: 28,
              fontWeight: 600,
              padding: [8, 0, 8, 0]
            },
            x: {
              fontSize: 16,
              color: statusInfo.statusColor
            }
          }
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: [
              [0, '#e9e9e9'],
              [1, '#e9e9e9']
            ],
            width: 6
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: true,
          length: 6,
          lineStyle: {
            color: '#fff',
            width: 3
          }
        },
        axisLabel: {
          show: false
        }
      }
    ]
  }
}

// 初始化仪表盘
const initGauges = () => {
  if (coGaugeRef.value) {
    coGaugeInstance.value = echarts.init(coGaugeRef.value)
    const coOption = createGaugeOption(props.coValue, 'CO', 100)
    coGaugeInstance.value.setOption(coOption)
  }

  if (co2GaugeRef.value) {
    co2GaugeInstance.value = echarts.init(co2GaugeRef.value)
    const co2Option = createGaugeOption(props.co2Value, 'CO₂', 100)
    co2GaugeInstance.value.setOption(co2Option)
  }
}

// 更新仪表盘数据
const updateGauges = () => {
  if (coGaugeInstance.value) {
    const coOption = createGaugeOption(props.coValue, 'CO', 100)
    coGaugeInstance.value.setOption(coOption)
  }

  if (co2GaugeInstance.value) {
    const co2Option = createGaugeOption(props.co2Value, 'CO₂', 100)
    co2GaugeInstance.value.setOption(co2Option)
  }
}

// 监听窗口大小变化
const handleResize = () => {
  if (coGaugeInstance.value) {
    coGaugeInstance.value.resize()
  }
  if (co2GaugeInstance.value) {
    co2GaugeInstance.value.resize()
  }
}

// 监听props变化
watch(() => [props.coValue, props.co2Value], () => {
  updateGauges()
}, { deep: true })

watch(() => props.initialDate, (newDate) => {
  selectedDate.value = newDate
})

// 生命周期
onMounted(() => {
  nextTick(() => {
    initGauges()
    window.addEventListener('resize', handleResize)
  })
})

onUnmounted(() => {
  if (coGaugeInstance.value) {
    coGaugeInstance.value.dispose()
  }
  if (co2GaugeInstance.value) {
    co2GaugeInstance.value.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.gas-monitor-container {
  width: 100%;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// 头部区域
.header-section {
  margin-bottom: 10px;
  padding-bottom: 15px;
}

.left-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.realtime-label {
  color: #333333;
  font-size: 18px;
  font-weight: 600;
}

.date-picker-wrapper {
  display: flex;
  align-items: center;
}

.custom-date-picker {
  width: 150px;

  :deep(.el-input__wrapper) {
    background: #ffffff;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-shadow: none;

    &:hover {
      border-color: #4A90E2;
    }

    &.is-focus {
      border-color: #4A90E2;
    }
  }

  :deep(.el-input__inner) {
    color: #333333;
    font-size: 14px;

    &::placeholder {
      color: #a8abb2;
    }
  }
}

// 数据展示区
.data-display-section {
  display: flex;
  gap: 20px;
  justify-content: space-between;
  align-items: center;
  min-height: 280px;
}

.gauge-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f8faff 0%, #f0f7ff 100%);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e6f4ff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

.gauge-chart {
  width: 100%;
  height: 240px;
  min-width: 200px;
}


// 响应式设计
@media (max-width: 768px) {
  .data-display-section {
    flex-direction: column;
    gap: 20px;
  }

  .gauge-container {
    padding: 15px;
  }

  .gauge-chart {
    height: 200px;
  }

  .header-section {
    .left-section {
      flex-direction: column;
      gap: 10px;
      align-items: flex-start;
    }
  }
}

@media (max-width: 480px) {
  .gas-monitor-container {
    padding: 15px;
  }

  .data-display-section {
    gap: 15px;
  }

  .gauge-container {
    padding: 10px;
  }

  .gauge-chart {
    height: 180px;
  }
}
</style>
